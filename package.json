{"name": "view", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@primevue/themes": "^4.3.8", "axios": "^1.11.0", "pinia": "^3.0.3", "primeicons": "^7.0.0", "primevue": "^4.3.8", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^24.3.1", "@typescript-eslint/eslint-plugin": "^8.42.0", "@typescript-eslint/parser": "^8.42.0", "@vitejs/plugin-vue": "^6.0.1", "@vue/eslint-config-typescript": "^14.6.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.34.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-vue": "^10.4.0", "prettier": "^3.6.2", "typescript": "~5.8.3", "vite": "^7.1.2", "vue-tsc": "^3.0.5"}}