/**
 * 用户状态管理
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 用户信息接口
export interface UserInfo {
  id: number
  username: string
  email: string
  avatar?: string
  nickname?: string
}

export const useUserStore = defineStore('user', () => {
  // State
  const userInfo = ref<UserInfo | null>(null)
  const token = ref<string>('')

  // Getters
  const isLoggedIn = computed(() => !!token.value)
  const userName = computed(() => userInfo.value?.username || '')

  // Actions
  const setToken = (newToken: string) => {
    token.value = newToken
    localStorage.setItem('token', newToken)
  }

  const setUserInfo = (info: UserInfo) => {
    userInfo.value = info
    localStorage.setItem('userInfo', JSON.stringify(info))
  }

  const login = async (loginData: { username: string; password: string }) => {
    try {
      // 这里调用登录 API
      // const response = await userApi.login(loginData)
      
      // 模拟登录
      const mockToken = 'mock-token-' + Date.now()
      const mockUserInfo: UserInfo = {
        id: 1,
        username: loginData.username,
        email: `${loginData.username}@example.com`,
        nickname: loginData.username
      }

      setToken(mockToken)
      setUserInfo(mockUserInfo)

      return { success: true }
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  }

  const logout = () => {
    token.value = ''
    userInfo.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
  }

  const initUserInfo = () => {
    const savedToken = localStorage.getItem('token')
    const savedUserInfo = localStorage.getItem('userInfo')

    if (savedToken) {
      token.value = savedToken
    }

    if (savedUserInfo) {
      try {
        userInfo.value = JSON.parse(savedUserInfo)
      } catch (error) {
        console.error('解析用户信息失败:', error)
      }
    }
  }

  return {
    // State
    userInfo,
    token,
    // Getters
    isLoggedIn,
    userName,
    // Actions
    setToken,
    setUserInfo,
    login,
    logout,
    initUserInfo
  }
})
