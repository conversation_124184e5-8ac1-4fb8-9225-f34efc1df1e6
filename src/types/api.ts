/**
 * 后端统一返回类型定义 - 匹配 ResponseData<T>
 */

// 后端统一响应格式
export interface ResponseData<T = any> {
  success: boolean
  errCode: number
  message: string
  warnings?: string[]
  data: T
  pageInfo?: PageInfo
  exception?: any
}

// 分页信息
export interface PageInfo {
  pageSize: number
  pageNo: number
  total: number
  countTotal: boolean
}

// 分页请求参数
export interface PageParams {
  pageNo?: number
  pageSize?: number
  [key: string]: any
}
