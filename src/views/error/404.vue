<template>
  <div class="error-container">
    <div class="error-content">
      <div class="error-icon">
        <i class="pi pi-exclamation-triangle"></i>
      </div>
      <h1>404</h1>
      <h2>页面不存在</h2>
      <p>抱歉，您访问的页面不存在或已被删除。</p>
      <Button 
        label="返回首页" 
        icon="pi pi-home" 
        @click="goHome"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/dashboard')
}
</script>

<style scoped>
.error-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.error-content {
  max-width: 500px;
  padding: 2rem;
}

.error-icon {
  font-size: 4rem;
  color: var(--orange-500);
  margin-bottom: 1rem;
}

.error-content h1 {
  font-size: 6rem;
  font-weight: 700;
  margin: 0;
  color: var(--text-color);
}

.error-content h2 {
  font-size: 2rem;
  margin: 1rem 0;
  color: var(--text-color);
}

.error-content p {
  font-size: 1.1rem;
  color: var(--text-color-secondary);
  margin-bottom: 2rem;
}
</style>
