<template>
  <div class="dashboard">
    <div class="welcome-section">
      <Card>
        <template #content>
          <div class="welcome-content">
            <div class="welcome-text">
              <h1>欢迎回来，{{ userName }}！</h1>
              <p>今天是个美好的一天，开始您的工作吧。</p>
            </div>
            <div class="welcome-actions">
              <Button 
                label="退出登录" 
                icon="pi pi-sign-out" 
                severity="secondary"
                @click="handleLogout"
              />
            </div>
          </div>
        </template>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/modules/user'

const router = useRouter()
const userStore = useUserStore()

const userName = computed(() => userStore.userName || '用户')

const handleLogout = () => {
  userStore.logout()
  router.push('/login')
}
</script>

<style scoped>
.dashboard {
  padding: 2rem;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-text h1 {
  margin: 0 0 0.5rem 0;
  color: var(--text-color);
}

.welcome-text p {
  margin: 0;
  color: var(--text-color-secondary);
}

@media (max-width: 768px) {
  .welcome-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}
</style>
