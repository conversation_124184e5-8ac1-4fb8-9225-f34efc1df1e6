<template>
  <div class="login-container">
    <div class="login-wrapper">
      <!-- 左侧装饰区域 -->
      <div class="login-decoration">
        <div class="decoration-content">
          <h1 class="brand-title">{{ appTitle }}</h1>
          <p class="brand-subtitle">现代化的管理系统</p>
          <div class="decoration-graphic">
            <i class="pi pi-shield" style="font-size: 4rem; opacity: 0.3;"></i>
          </div>
        </div>
      </div>

      <!-- 右侧登录表单 -->
      <div class="login-form-section">
        <Card class="login-card">
          <template #header>
            <div class="login-header">
              <div class="login-icon">
                <i class="pi pi-user-plus"></i>
              </div>
              <h2>欢迎登录</h2>
              <p>请输入您的邮箱和密码</p>
            </div>
          </template>

          <template #content>
            <form @submit.prevent="handleLogin" class="login-form">
              <!-- 邮箱输入 -->
              <div class="form-field">
                <label for="email">邮箱地址</label>
                <div class="input-wrapper">
                  <i class="pi pi-envelope input-icon"></i>
                  <InputText
                    id="email"
                    v-model="loginForm.email"
                    type="email"
                    placeholder="请输入邮箱地址"
                    :class="{ 'p-invalid': emailError }"
                    class="w-full"
                    @blur="validateEmail"
                  />
                </div>
                <small v-if="emailError" class="p-error">{{ emailError }}</small>
              </div>

              <!-- 密码输入 -->
              <div class="form-field">
                <label for="password">密码</label>
                <div class="input-wrapper">
                  <i class="pi pi-lock input-icon"></i>
                  <Password
                    id="password"
                    v-model="loginForm.password"
                    placeholder="请输入密码"
                    :class="{ 'p-invalid': passwordError }"
                    class="w-full"
                    :feedback="false"
                    toggleMask
                    @blur="validatePassword"
                  />
                </div>
                <small v-if="passwordError" class="p-error">{{ passwordError }}</small>
              </div>

              <!-- 记住我 -->
              <div class="form-options">
                <div class="remember-me">
                  <Checkbox
                    v-model="loginForm.rememberMe"
                    inputId="rememberMe"
                    :binary="true"
                  />
                  <label for="rememberMe">记住我</label>
                </div>
                <a href="#" class="forgot-password">忘记密码？</a>
              </div>

              <!-- 登录按钮 -->
              <Button
                type="submit"
                label="登录"
                icon="pi pi-sign-in"
                class="login-button"
                :loading="loading"
                :disabled="!isFormValid"
              />
            </form>
          </template>
        </Card>
      </div>
    </div>

    <!-- Toast 消息 -->
    <Toast />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'primevue/usetoast'
import { useUserStore } from '@/store/modules/user'
import { getAppTitle } from '@/utils/env'

const router = useRouter()
const toast = useToast()
const userStore = useUserStore()

const appTitle = getAppTitle()
const loading = ref(false)

// 表单数据
const loginForm = ref({
  email: '',
  password: '',
  rememberMe: false
})

// 表单验证错误
const emailError = ref('')
const passwordError = ref('')

// 验证邮箱
const validateEmail = () => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!loginForm.value.email) {
    emailError.value = '请输入邮箱地址'
  } else if (!emailRegex.test(loginForm.value.email)) {
    emailError.value = '请输入有效的邮箱地址'
  } else {
    emailError.value = ''
  }
}

// 验证密码
const validatePassword = () => {
  if (!loginForm.value.password) {
    passwordError.value = '请输入密码'
  } else if (loginForm.value.password.length < 6) {
    passwordError.value = '密码至少6位'
  } else {
    passwordError.value = ''
  }
}

// 表单是否有效
const isFormValid = computed(() => {
  return loginForm.value.email &&
         loginForm.value.password &&
         !emailError.value &&
         !passwordError.value
})

// 处理登录
const handleLogin = async () => {
  validateEmail()
  validatePassword()

  if (!isFormValid.value) return

  loading.value = true

  try {
    await userStore.login({
      username: loginForm.value.email,
      password: loginForm.value.password
    })

    toast.add({
      severity: 'success',
      summary: '登录成功',
      detail: '欢迎回来！',
      life: 3000
    })

    router.push('/dashboard')
  } catch (error) {
    toast.add({
      severity: 'error',
      summary: '登录失败',
      detail: '邮箱或密码错误',
      life: 3000
    })
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.login-wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
  max-width: 1000px;
  width: 100%;
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.login-decoration {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  position: relative;
}

.decoration-content {
  text-align: center;
}

.brand-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.brand-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 2rem;
}

.decoration-graphic {
  margin-top: 2rem;
}

.login-form-section {
  padding: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-card {
  width: 100%;
  max-width: 400px;
  border: none;
  box-shadow: none;
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-icon {
  width: 60px;
  height: 60px;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  color: white;
  font-size: 1.5rem;
}

.login-header h2 {
  margin: 0 0 0.5rem 0;
  color: var(--text-color);
  font-weight: 600;
}

.login-header p {
  margin: 0;
  color: var(--text-color-secondary);
}

.form-field {
  margin-bottom: 1.5rem;
}

.form-field label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
}

.input-wrapper {
  position: relative;
}

.input-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-color-secondary);
  z-index: 1;
}

.input-wrapper :deep(.p-inputtext) {
  padding-left: 2.5rem;
}

.input-wrapper :deep(.p-password) {
  width: 100%;
}

.input-wrapper :deep(.p-password .p-inputtext) {
  padding-left: 2.5rem;
  width: 100%;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.remember-me {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.remember-me label {
  margin: 0;
  cursor: pointer;
}

.forgot-password {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.9rem;
}

.forgot-password:hover {
  text-decoration: underline;
}

.login-button {
  width: 100%;
  padding: 0.75rem;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
}

.w-full {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-wrapper {
    grid-template-columns: 1fr;
    max-width: 400px;
  }

  .login-decoration {
    display: none;
  }

  .login-container {
    padding: 1rem;
  }

  .login-form-section {
    padding: 2rem;
  }
}
</style>
