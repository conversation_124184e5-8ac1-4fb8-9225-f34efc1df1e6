/**
 * 本地存储工具
 */

/**
 * localStorage 封装
 */
export const localStorage = {
  /**
   * 设置数据
   */
  set<T>(key: string, value: T): void {
    try {
      const serializedValue = JSON.stringify(value)
      window.localStorage.setItem(key, serializedValue)
    } catch (error) {
      console.error('localStorage set error:', error)
    }
  },

  /**
   * 获取数据
   */
  get<T>(key: string, defaultValue?: T): T | null {
    try {
      const item = window.localStorage.getItem(key)
      if (item === null) return defaultValue || null
      return JSON.parse(item)
    } catch (error) {
      console.error('localStorage get error:', error)
      return defaultValue || null
    }
  },

  /**
   * 删除数据
   */
  remove(key: string): void {
    try {
      window.localStorage.removeItem(key)
    } catch (error) {
      console.error('localStorage remove error:', error)
    }
  },

  /**
   * 清空所有数据
   */
  clear(): void {
    try {
      window.localStorage.clear()
    } catch (error) {
      console.error('localStorage clear error:', error)
    }
  },

  /**
   * 获取所有键名
   */
  keys(): string[] {
    try {
      return Object.keys(window.localStorage)
    } catch (error) {
      console.error('localStorage keys error:', error)
      return []
    }
  }
}

/**
 * sessionStorage 封装
 */
export const sessionStorage = {
  /**
   * 设置数据
   */
  set<T>(key: string, value: T): void {
    try {
      const serializedValue = JSON.stringify(value)
      window.sessionStorage.setItem(key, serializedValue)
    } catch (error) {
      console.error('sessionStorage set error:', error)
    }
  },

  /**
   * 获取数据
   */
  get<T>(key: string, defaultValue?: T): T | null {
    try {
      const item = window.sessionStorage.getItem(key)
      if (item === null) return defaultValue || null
      return JSON.parse(item)
    } catch (error) {
      console.error('sessionStorage get error:', error)
      return defaultValue || null
    }
  },

  /**
   * 删除数据
   */
  remove(key: string): void {
    try {
      window.sessionStorage.removeItem(key)
    } catch (error) {
      console.error('sessionStorage remove error:', error)
    }
  },

  /**
   * 清空所有数据
   */
  clear(): void {
    try {
      window.sessionStorage.clear()
    } catch (error) {
      console.error('sessionStorage clear error:', error)
    }
  },

  /**
   * 获取所有键名
   */
  keys(): string[] {
    try {
      return Object.keys(window.sessionStorage)
    } catch (error) {
      console.error('sessionStorage keys error:', error)
      return []
    }
  }
}

/**
 * 带过期时间的存储
 */
interface StorageItem<T> {
  value: T
  expires?: number
}

export const storageWithExpiry = {
  /**
   * 设置数据（带过期时间）
   */
  set<T>(key: string, value: T, expiryInMinutes?: number): void {
    const item: StorageItem<T> = {
      value,
      expires: expiryInMinutes ? Date.now() + expiryInMinutes * 60 * 1000 : undefined
    }
    localStorage.set(key, item)
  },

  /**
   * 获取数据（检查过期时间）
   */
  get<T>(key: string, defaultValue?: T): T | null {
    const item = localStorage.get<StorageItem<T>>(key)
    
    if (!item) return defaultValue || null
    
    // 检查是否过期
    if (item.expires && Date.now() > item.expires) {
      localStorage.remove(key)
      return defaultValue || null
    }
    
    return item.value
  },

  /**
   * 删除数据
   */
  remove(key: string): void {
    localStorage.remove(key)
  },

  /**
   * 清理过期数据
   */
  clearExpired(): void {
    const keys = localStorage.keys()
    const now = Date.now()
    
    keys.forEach(key => {
      const item = localStorage.get<StorageItem<any>>(key)
      if (item && item.expires && now > item.expires) {
        localStorage.remove(key)
      }
    })
  }
}

/**
 * Cookie 操作
 */
export const cookie = {
  /**
   * 设置 Cookie
   */
  set(name: string, value: string, days?: number, path = '/'): void {
    let expires = ''
    if (days) {
      const date = new Date()
      date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000)
      expires = `; expires=${date.toUTCString()}`
    }
    document.cookie = `${name}=${value}${expires}; path=${path}`
  },

  /**
   * 获取 Cookie
   */
  get(name: string): string | null {
    const nameEQ = `${name}=`
    const ca = document.cookie.split(';')
    
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i]
      while (c.charAt(0) === ' ') c = c.substring(1, c.length)
      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length)
    }
    
    return null
  },

  /**
   * 删除 Cookie
   */
  remove(name: string, path = '/'): void {
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}`
  },

  /**
   * 获取所有 Cookie
   */
  getAll(): Record<string, string> {
    const cookies: Record<string, string> = {}
    const ca = document.cookie.split(';')
    
    ca.forEach(cookie => {
      const [name, value] = cookie.trim().split('=')
      if (name && value) {
        cookies[name] = value
      }
    })
    
    return cookies
  }
}
