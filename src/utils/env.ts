/**
 * 环境变量工具
 */

// 获取环境变量
export const getEnv = (key: keyof ImportMetaEnv): string => {
  return import.meta.env[key] || ''
}

// 判断是否为开发环境
export const isDev = (): boolean => {
  return import.meta.env.DEV
}

// 判断是否为生产环境
export const isProd = (): boolean => {
  return import.meta.env.PROD
}

// 获取 API 基础地址
export const getApiBaseUrl = (): string => {
  return getEnv('VITE_API_BASE_URL')
}

// 获取应用标题
export const getAppTitle = (): string => {
  return getEnv('VITE_APP_TITLE')
}

// 获取应用版本
export const getAppVersion = (): string => {
  return getEnv('VITE_APP_VERSION')
}
