/**
 * 格式化工具函数
 */

/**
 * 格式化日期
 */
export function formatDate(
  date: Date | string | number,
  format = 'YYYY-MM-DD HH:mm:ss'
): string {
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''

  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')

  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 格式化相对时间
 */
export function formatRelativeTime(date: Date | string | number): string {
  const now = new Date()
  const target = new Date(date)
  const diff = now.getTime() - target.getTime()

  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  const week = 7 * day
  const month = 30 * day
  const year = 365 * day

  if (diff < minute) return '刚刚'
  if (diff < hour) return `${Math.floor(diff / minute)}分钟前`
  if (diff < day) return `${Math.floor(diff / hour)}小时前`
  if (diff < week) return `${Math.floor(diff / day)}天前`
  if (diff < month) return `${Math.floor(diff / week)}周前`
  if (diff < year) return `${Math.floor(diff / month)}个月前`
  return `${Math.floor(diff / year)}年前`
}

/**
 * 格式化数字
 */
export function formatNumber(
  num: number,
  options: {
    decimals?: number
    separator?: string
    prefix?: string
    suffix?: string
  } = {}
): string {
  const { decimals = 0, separator = ',', prefix = '', suffix = '' } = options

  const parts = num.toFixed(decimals).split('.')
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, separator)

  return prefix + parts.join('.') + suffix
}

/**
 * 格式化货币
 */
export function formatCurrency(
  amount: number,
  currency = '¥',
  decimals = 2
): string {
  return formatNumber(amount, {
    decimals,
    separator: ',',
    prefix: currency
  })
}

/**
 * 格式化百分比
 */
export function formatPercent(num: number, decimals = 2): string {
  return formatNumber(num * 100, {
    decimals,
    suffix: '%'
  })
}

/**
 * 格式化手机号
 */
export function formatPhone(phone: string): string {
  const cleaned = phone.replace(/\D/g, '')
  if (cleaned.length === 11) {
    return cleaned.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3')
  }
  return phone
}

/**
 * 隐藏手机号中间四位
 */
export function maskPhone(phone: string): string {
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

/**
 * 隐藏邮箱
 */
export function maskEmail(email: string): string {
  const [username, domain] = email.split('@')
  if (username.length <= 2) return email
  
  const maskedUsername = username.charAt(0) + 
    '*'.repeat(username.length - 2) + 
    username.charAt(username.length - 1)
  
  return `${maskedUsername}@${domain}`
}

/**
 * 格式化身份证号
 */
export function formatIdCard(idCard: string): string {
  return idCard.replace(/(\d{6})(\d{8})(\d{4})/, '$1 $2 $3')
}

/**
 * 隐藏身份证号
 */
export function maskIdCard(idCard: string): string {
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
}

/**
 * 格式化银行卡号
 */
export function formatBankCard(cardNumber: string): string {
  return cardNumber.replace(/(\d{4})(?=\d)/g, '$1 ')
}

/**
 * 隐藏银行卡号
 */
export function maskBankCard(cardNumber: string): string {
  const cleaned = cardNumber.replace(/\s/g, '')
  if (cleaned.length < 8) return cardNumber
  
  const start = cleaned.substring(0, 4)
  const end = cleaned.substring(cleaned.length - 4)
  const middle = '*'.repeat(cleaned.length - 8)
  
  return `${start} ${middle} ${end}`
}

/**
 * 首字母大写
 */
export function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
}

/**
 * 驼峰转短横线
 */
export function camelToKebab(str: string): string {
  return str.replace(/([A-Z])/g, '-$1').toLowerCase()
}

/**
 * 短横线转驼峰
 */
export function kebabToCamel(str: string): string {
  return str.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase())
}
