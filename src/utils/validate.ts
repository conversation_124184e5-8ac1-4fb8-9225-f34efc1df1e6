/**
 * 验证工具函数
 */

/**
 * 验证邮箱
 */
export function isEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 验证手机号
 */
export function isPhone(phone: string): boolean {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 验证密码强度
 */
export function isStrongPassword(password: string): boolean {
  // 至少8位，包含大小写字母和数字
  const strongRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/
  return strongRegex.test(password)
}

/**
 * 验证用户名
 */
export function isUsername(username: string): boolean {
  // 3-20位字母、数字、下划线
  const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/
  return usernameRegex.test(username)
}

/**
 * 验证身份证号
 */
export function isIdCard(idCard: string): boolean {
  const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
  return idCardRegex.test(idCard)
}

/**
 * 验证银行卡号
 */
export function isBankCard(cardNumber: string): boolean {
  const cleaned = cardNumber.replace(/\s/g, '')
  const bankCardRegex = /^\d{16,19}$/
  return bankCardRegex.test(cleaned)
}

/**
 * 验证URL
 */
export function isUrl(url: string): boolean {
  const urlRegex = /^https?:\/\/.+/
  return urlRegex.test(url)
}

/**
 * 验证IP地址
 */
export function isIP(ip: string): boolean {
  const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
  return ipRegex.test(ip)
}

/**
 * 验证端口号
 */
export function isPort(port: string | number): boolean {
  const portNum = typeof port === 'string' ? parseInt(port, 10) : port
  return Number.isInteger(portNum) && portNum >= 1 && portNum <= 65535
}

/**
 * 验证中文
 */
export function isChinese(text: string): boolean {
  const chineseRegex = /^[\u4e00-\u9fa5]+$/
  return chineseRegex.test(text)
}

/**
 * 验证英文
 */
export function isEnglish(text: string): boolean {
  const englishRegex = /^[a-zA-Z]+$/
  return englishRegex.test(text)
}

/**
 * 验证数字
 */
export function isNumber(value: any): boolean {
  return !isNaN(value) && !isNaN(parseFloat(value))
}

/**
 * 验证整数
 */
export function isInteger(value: any): boolean {
  return Number.isInteger(Number(value))
}

/**
 * 验证正整数
 */
export function isPositiveInteger(value: any): boolean {
  const num = Number(value)
  return Number.isInteger(num) && num > 0
}

/**
 * 验证非负整数
 */
export function isNonNegativeInteger(value: any): boolean {
  const num = Number(value)
  return Number.isInteger(num) && num >= 0
}

/**
 * 验证小数
 */
export function isDecimal(value: any, decimals?: number): boolean {
  if (!isNumber(value)) return false
  
  if (decimals !== undefined) {
    const decimalRegex = new RegExp(`^\\d+\\.\\d{1,${decimals}}$`)
    return decimalRegex.test(String(value))
  }
  
  return String(value).includes('.')
}

/**
 * 验证范围
 */
export function isInRange(value: number, min: number, max: number): boolean {
  return value >= min && value <= max
}

/**
 * 验证长度
 */
export function isLengthInRange(str: string, min: number, max: number): boolean {
  return str.length >= min && str.length <= max
}

/**
 * 验证是否为空
 */
export function isEmpty(value: any): boolean {
  if (value === null || value === undefined) return true
  if (typeof value === 'string') return value.trim() === ''
  if (Array.isArray(value)) return value.length === 0
  if (typeof value === 'object') return Object.keys(value).length === 0
  return false
}

/**
 * 验证是否不为空
 */
export function isNotEmpty(value: any): boolean {
  return !isEmpty(value)
}

/**
 * 验证文件类型
 */
export function isValidFileType(file: File, allowedTypes: string[]): boolean {
  return allowedTypes.includes(file.type)
}

/**
 * 验证文件大小
 */
export function isValidFileSize(file: File, maxSize: number): boolean {
  return file.size <= maxSize
}

/**
 * 综合表单验证
 */
export interface ValidationRule {
  required?: boolean
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (value: any) => boolean
  message?: string
}

export function validateField(value: any, rules: ValidationRule[]): string | null {
  for (const rule of rules) {
    if (rule.required && isEmpty(value)) {
      return rule.message || '此字段为必填项'
    }
    
    if (!isEmpty(value)) {
      if (rule.min !== undefined && String(value).length < rule.min) {
        return rule.message || `最少需要${rule.min}个字符`
      }
      
      if (rule.max !== undefined && String(value).length > rule.max) {
        return rule.message || `最多允许${rule.max}个字符`
      }
      
      if (rule.pattern && !rule.pattern.test(String(value))) {
        return rule.message || '格式不正确'
      }
      
      if (rule.validator && !rule.validator(value)) {
        return rule.message || '验证失败'
      }
    }
  }
  
  return null
}
