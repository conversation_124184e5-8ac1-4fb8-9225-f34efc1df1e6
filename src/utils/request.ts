/**
 * Axios 请求封装
 */
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'
import { ResponseData } from '@/types/api'

// 创建 axios 实例
const request: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 添加 token
    const token = localStorage.getItem('token')
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }

    return config
  },
  (error: AxiosError) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse<ResponseData>) => {
    const { data } = response

    // 请求成功，返回 data
    if (data.success) {
      return data.data
    }

    // 业务错误
    const errorMessage = data.message || '请求失败'
    console.error('业务错误:', errorMessage)
    return Promise.reject(new Error(errorMessage))
  },
  (error: AxiosError) => {
    const { response } = error
    let errorMessage = '网络错误'

    if (response) {
      switch (response.status) {
        case 401:
          errorMessage = '登录已过期'
          localStorage.removeItem('token')
          window.location.href = '/login'
          break
        case 403:
          errorMessage = '没有权限'
          break
        case 404:
          errorMessage = '资源不存在'
          break
        case 500:
          errorMessage = '服务器错误'
          break
        default:
          errorMessage = `请求失败 (${response.status})`
      }
    }

    console.error('HTTP错误:', errorMessage)
    return Promise.reject(error)
  }
)

export default request
