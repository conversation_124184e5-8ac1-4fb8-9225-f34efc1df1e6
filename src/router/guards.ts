/**
 * 路由守卫
 */
import type { Router } from 'vue-router'

export function setupRouterGuards(router: Router) {
  // 全局前置守卫
  router.beforeEach((to, from, next) => {
    // 设置页面标题
    if (to.meta?.title) {
      document.title = `${to.meta.title} - ${import.meta.env.VITE_APP_TITLE}`
    }

    // 检查是否需要登录
    if (to.meta?.requiresAuth) {
      const token = localStorage.getItem('token')
      if (!token) {
        next('/login')
        return
      }
    }

    // 如果已登录访问登录页，跳转到首页
    if (to.path === '/login') {
      const token = localStorage.getItem('token')
      if (token) {
        next('/dashboard')
        return
      }
    }

    next()
  })

  // 全局后置守卫
  router.afterEach(() => {
    // 可以在这里添加页面加载完成后的逻辑
  })
}
