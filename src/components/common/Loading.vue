<template>
  <div v-if="visible" :class="loadingClass">
    <div class="loading-content">
      <div class="loading-spinner">
        <i class="pi pi-spin pi-spinner" :style="{ fontSize: size }"></i>
      </div>
      <div v-if="text" class="loading-text">{{ text }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  visible?: boolean
  text?: string
  size?: string
  overlay?: boolean
  background?: string
}

const props = withDefaults(defineProps<Props>(), {
  visible: true,
  text: '加载中...',
  size: '2rem',
  overlay: false,
  background: 'rgba(255, 255, 255, 0.8)'
})

const loadingClass = computed(() => [
  'loading-container',
  {
    'loading-overlay': props.overlay
  }
])
</script>

<style scoped>
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: v-bind(background);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.loading-spinner {
  color: var(--primary-color);
}

.loading-text {
  color: var(--text-color);
  font-size: 0.9rem;
}
</style>
