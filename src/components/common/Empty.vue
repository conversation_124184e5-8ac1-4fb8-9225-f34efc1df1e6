<template>
  <div class="empty-container">
    <div class="empty-content">
      <div class="empty-icon">
        <i :class="iconClass" :style="{ fontSize: iconSize }"></i>
      </div>
      <div class="empty-title">{{ title }}</div>
      <div v-if="description" class="empty-description">{{ description }}</div>
      <div v-if="$slots.action" class="empty-action">
        <slot name="action"></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  title?: string
  description?: string
  icon?: string
  iconSize?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '暂无数据',
  description: '',
  icon: 'pi pi-inbox',
  iconSize: '4rem'
})

const iconClass = computed(() => [
  'empty-icon-element',
  props.icon
])
</script>

<style scoped>
.empty-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 2rem;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 400px;
}

.empty-icon {
  margin-bottom: 1rem;
}

.empty-icon-element {
  color: var(--text-color-secondary);
  opacity: 0.6;
}

.empty-title {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.empty-description {
  font-size: 0.9rem;
  color: var(--text-color-secondary);
  line-height: 1.5;
  margin-bottom: 1.5rem;
}

.empty-action {
  margin-top: 1rem;
}
</style>
