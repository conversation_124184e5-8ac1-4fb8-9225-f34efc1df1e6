import { createApp } from 'vue'
import './style.css'
import App from './App.vue'

// Router
import router from './router'

// Store
import store from './store'

// PrimeVue
import PrimeVue from 'primevue/config'
import Aura from '@primevue/themes/aura'

// PrimeVue Components (按需引入，这里先引入几个常用的)
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import Card from 'primevue/card'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import Dialog from 'primevue/dialog'
import Toast from 'primevue/toast'
import ToastService from 'primevue/toastservice'

// PrimeIcons
import 'primeicons/primeicons.css'

const app = createApp(App)

// 使用 Router
app.use(router)

// 使用 Store
app.use(store)

// 配置 PrimeVue
app.use(PrimeVue, {
  theme: {
    preset: Aura,
    options: {
      prefix: 'p',
      darkModeSelector: '.p-dark',
      cssLayer: false
    }
  }
})

// 注册 PrimeVue 服务
app.use(ToastService)

// 注册 PrimeVue 组件
app.component('Button', Button)
app.component('InputText', InputText)
app.component('Card', Card)
app.component('DataTable', DataTable)
app.component('Column', Column)
app.component('Dialog', Dialog)
app.component('Toast', Toast)

app.mount('#app')
