# 📁 项目目录结构说明

## 🏗️ 整体架构

```
src/
├── api/                    # API 接口管理
├── assets/                 # 静态资源
├── components/             # 组件库
├── constants/              # 常量定义
├── directives/             # 自定义指令
├── hooks/                  # 组合式函数
├── plugins/                # 插件配置
├── router/                 # 路由配置
├── store/                  # 状态管理
├── types/                  # TypeScript 类型定义
├── utils/                  # 工具函数
├── views/                  # 页面组件
├── App.vue                 # 根组件
└── main.ts                 # 应用入口
```

## 📂 详细说明

### 🔌 api/ - API 接口管理
```
api/
├── index.ts               # 通用请求方法
├── user.ts                # 用户相关接口
├── auth.ts                # 认证相关接口
└── modules/               # 业务模块接口
    ├── product.ts         # 产品模块
    ├── order.ts           # 订单模块
    └── system.ts          # 系统模块
```

### 🎨 assets/ - 静态资源
```
assets/
├── images/                # 图片资源
│   ├── logo.png
│   ├── avatar/            # 头像图片
│   └── icons/             # 图标图片
├── icons/                 # SVG 图标
│   ├── index.ts           # 图标导出
│   └── svg/               # SVG 文件
└── styles/                # 样式文件
    ├── index.css          # 全局样式
    ├── variables.css      # CSS 变量
    ├── mixins.css         # CSS 混入
    └── components.css     # 组件样式
```

### 🧩 components/ - 组件库
```
components/
├── common/                # 通用组件
│   ├── Loading/           # 加载组件
│   ├── Empty/             # 空状态组件
│   ├── ErrorBoundary/     # 错误边界
│   └── Confirm/           # 确认对话框
├── layout/                # 布局组件
│   ├── Header/            # 头部组件
│   ├── Sidebar/           # 侧边栏
│   ├── Footer/            # 底部组件
│   └── Layout/            # 主布局
└── business/              # 业务组件
    ├── UserCard/          # 用户卡片
    ├── ProductList/       # 产品列表
    └── OrderTable/        # 订单表格
```

### 📄 constants/ - 常量定义
```
constants/
├── index.ts               # 通用常量
├── api.ts                 # API 相关常量
├── storage.ts             # 存储键名常量
└── enums.ts               # 枚举定义
```

### 🎯 directives/ - 自定义指令
```
directives/
├── index.ts               # 指令注册
├── loading.ts             # 加载指令
├── permission.ts          # 权限指令
└── clickOutside.ts        # 点击外部指令
```

### 🪝 hooks/ - 组合式函数
```
hooks/
├── index.ts               # 导出所有 hooks
├── useRequest.ts          # 请求 hook
├── useAuth.ts             # 认证 hook
├── usePagination.ts       # 分页 hook
└── useLocalStorage.ts     # 本地存储 hook
```

### 🔌 plugins/ - 插件配置
```
plugins/
├── index.ts               # 插件注册
├── primevue.ts            # PrimeVue 配置
└── directives.ts          # 指令插件
```

### 🛣️ router/ - 路由配置
```
router/
├── index.ts               # 路由主配置
├── routes.ts              # 路由定义
├── guards.ts              # 路由守卫
└── modules/               # 路由模块
    ├── auth.ts            # 认证路由
    ├── dashboard.ts       # 仪表盘路由
    └── system.ts          # 系统路由
```

### 🗄️ store/ - 状态管理
```
store/
├── index.ts               # Store 主配置
└── modules/               # Store 模块
    ├── user.ts            # 用户状态
    ├── app.ts             # 应用状态
    └── auth.ts            # 认证状态
```

### 📝 types/ - TypeScript 类型
```
types/
├── index.ts               # 通用类型
├── api.ts                 # API 类型
├── user.ts                # 用户类型
├── router.ts              # 路由类型
└── global.d.ts            # 全局类型声明
```

### 🛠️ utils/ - 工具函数
```
utils/
├── index.ts               # 工具函数导出
├── request.ts             # 请求工具
├── storage.ts             # 存储工具
├── format.ts              # 格式化工具
├── validate.ts            # 验证工具
└── common.ts              # 通用工具
```

### 📱 views/ - 页面组件
```
views/
├── auth/                  # 认证页面
│   ├── Login.vue          # 登录页
│   ├── Register.vue       # 注册页
│   └── ForgotPassword.vue # 忘记密码
├── dashboard/             # 仪表盘
│   ├── Index.vue          # 首页
│   └── Analytics.vue      # 数据分析
├── system/                # 系统管理
│   ├── User.vue           # 用户管理
│   ├── Role.vue           # 角色管理
│   └── Menu.vue           # 菜单管理
└── error/                 # 错误页面
    ├── 404.vue            # 404 页面
    ├── 403.vue            # 403 页面
    └── 500.vue            # 500 页面
```

## 🎯 命名规范

### 📁 文件夹命名
- 使用 **kebab-case** (短横线分隔)
- 例如: `user-management`, `order-list`

### 📄 文件命名
- **组件文件**: 使用 **PascalCase**
  - 例如: `UserCard.vue`, `ProductList.vue`
- **工具文件**: 使用 **camelCase**
  - 例如: `formatDate.ts`, `validateEmail.ts`
- **常量文件**: 使用 **camelCase**
  - 例如: `apiConstants.ts`, `storageKeys.ts`

### 🏷️ 变量命名
- **组件名**: PascalCase - `UserProfile`
- **函数名**: camelCase - `getUserInfo`
- **常量名**: UPPER_SNAKE_CASE - `API_BASE_URL`
- **类型名**: PascalCase - `UserInfo`, `ApiResponse`

## 📋 开发规范

1. **单一职责**: 每个文件只负责一个功能
2. **模块化**: 相关功能放在同一模块下
3. **可复用**: 通用功能抽取为公共组件/工具
4. **类型安全**: 充分利用 TypeScript 类型检查
5. **文档完善**: 重要函数和组件添加注释
