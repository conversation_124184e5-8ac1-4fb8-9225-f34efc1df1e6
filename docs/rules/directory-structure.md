# 🏗️ 目录结构规范

## 📁 核心目录

```
src/
├── api/           # API 接口
├── components/    # 组件库
│   ├── common/    # 通用组件
│   ├── layout/    # 布局组件
│   └── business/  # 业务组件
├── views/         # 页面组件
├── router/        # 路由配置
├── store/         # 状态管理
├── utils/         # 工具函数
├── types/         # 类型定义
├── constants/     # 常量
└── assets/        # 静态资源
```

## 📂 目录说明

**api/** - API 接口管理
```typescript
// api/user.ts
export const userApi = {
  login: (params: LoginParams) => http.post('/auth/login', params),
  getUserInfo: () => http.get('/user/profile')
}
```

**components/** - 组件库
- `common/` - 通用组件 (Loading, Empty)
- `layout/` - 布局组件 (Header, Sidebar)
- `business/` - 业务组件 (UserCard, ProductList)

**views/** - 页面组件
```
views/
├── auth/Login.vue
├── dashboard/Index.vue
└── user/List.vue
```

**utils/** - 工具函数
```typescript
// utils/format.ts
export function formatDate(date: Date): string {}
```

**constants/** - 常量定义
```typescript
export const API_ENDPOINTS = {
  USER_LOGIN: '/auth/login'
}
```

## 📋 命名规则

- **组件文件**: `PascalCase.vue` → `UserProfile.vue`
- **工具文件**: `camelCase.ts` → `formatDate.ts`
- **目录**: `kebab-case` → `user-management`

## 🎯 组织原则

1. **单一职责** - 每个目录只负责一个功能
2. **模块化** - 相关功能放在同一模块
3. **扁平化** - 避免过深的目录嵌套
4. **见名知意** - 目录名要清晰表达用途
