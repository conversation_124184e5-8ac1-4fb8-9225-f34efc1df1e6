# ✅ 代码质量规范

## 🔍 代码检查工具

**ESLint 配置**
```javascript
// .eslintrc.js
module.exports = {
  extends: [
    '@vue/eslint-config-typescript',
    '@vue/eslint-config-prettier'
  ],
  rules: {
    'vue/component-name-in-template-casing': ['error', 'PascalCase'],
    '@typescript-eslint/no-unused-vars': 'error',
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'warn'
  }
}
```

**Prettier 配置**
```json
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "printWidth": 100
}
```

## 📝 注释规范

**函数注释**
```typescript
/**
 * 格式化用户信息
 * @param user 用户对象
 * @param options 格式化选项
 * @returns 格式化后的字符串
 */
export function formatUserInfo(user: User, options: FormatOptions = {}): string {
  // 实现逻辑
}
```

**复杂逻辑注释**
```typescript
const calculateDiscount = (order: Order): number => {
  // 基础折扣：订单金额超过1000元享受5%折扣
  let discount = order.amount > 1000 ? order.amount * 0.05 : 0

  // VIP用户额外享受2%折扣
  if (order.user.isVip) {
    discount += order.amount * 0.02
  }

  return Math.min(discount, order.amount * 0.2)
}
```

## 🚀 性能优化

**Vue 组件优化**
```vue
<script setup lang="ts">
// 使用 computed 缓存计算结果
const expensiveValue = computed(() => heavyCalculation(props.data))

// 组件懒加载
const LazyComponent = defineAsyncComponent(() =>
  import('@/components/heavy/LazyComponent.vue')
)
</script>

<template>
  <!-- 使用 v-memo 优化列表渲染 -->
  <div
    v-for="item in items"
    :key="item.id"
    v-memo="[item.id, item.updateTime]"
  >
    {{ item.name }}
  </div>
</template>
```

**请求优化**
```typescript
// 请求防抖
export const debouncedSearch = debounce(async (keyword: string) => {
  const results = await searchApi.search(keyword)
  searchResults.value = results
}, 300)
```

## 🛡️ 安全规范

**XSS 防护**
```vue
<template>
  <!-- ✅ 使用 v-text 防止 XSS -->
  <div v-text="userInput"></div>

  <!-- ❌ 避免直接使用 v-html -->
  <div v-html="userInput"></div>
</template>
```

**输入验证**
```typescript
export const validateUserInput = (input: string): boolean => {
  if (input.length > 1000) return false
  if (/<script|javascript:/i.test(input)) return false
  return true
}
```

## 🧪 测试规范

**单元测试**
```typescript
import { formatDate } from '@/utils/format'

describe('formatDate', () => {
  it('应该正确格式化日期', () => {
    const date = new Date('2024-01-01T12:00:00Z')
    expect(formatDate(date, 'YYYY-MM-DD')).toBe('2024-01-01')
  })
})
```

**组件测试**
```typescript
import { mount } from '@vue/test-utils'
import UserCard from '@/components/business/UserCard.vue'

describe('UserCard', () => {
  it('应该渲染用户信息', () => {
    const wrapper = mount(UserCard, {
      props: { user: { name: 'John' } }
    })
    expect(wrapper.text()).toContain('John')
  })
})
```

## 📊 代码质量控制

**降低复杂度**
```typescript
// ✅ 使用映射降低圈复杂度
const getUserStatus = (user: User): string => {
  const statusMap = {
    active: '活跃',
    inactive: '非活跃',
    banned: '已封禁'
  }
  return statusMap[user.status] || '未知'
}
```

**拆分长函数**
```typescript
const processUserData = (users: User[]) => {
  const validUsers = filterValidUsers(users)
  const enrichedUsers = enrichUserData(validUsers)
  return sortUsersByName(enrichedUsers)
}
```

## 🔧 提交前检查

- [ ] ESLint 检查通过
- [ ] TypeScript 类型检查通过
- [ ] 添加必要注释
- [ ] 编写单元测试
- [ ] 遵循命名规范

## 📈 质量监控

```json
{
  "scripts": {
    "lint": "eslint src --ext .vue,.ts,.js",
    "type-check": "vue-tsc --noEmit",
    "test": "vitest",
    "build": "vue-tsc -b && vite build"
  }
}
```
