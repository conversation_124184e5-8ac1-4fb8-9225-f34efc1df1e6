# 📝 命名规范

## 📁 文件命名

**Vue 组件**: `PascalCase.vue`
```bash
✅ UserProfile.vue, ProductList.vue
❌ userProfile.vue, user-profile.vue
```

**TypeScript 文件**: `camelCase.ts`
```bash
✅ formatDate.ts, userService.ts
❌ format-date.ts, UserService.ts
```

**目录**: `kebab-case`
```bash
✅ user-management, order-history
❌ userManagement, user_management
```

## 🔤 变量命名

**普通变量**: `camelCase`
```typescript
const userName = 'john'
const isLoggedIn = true
const handleButtonClick = () => {}
```

**常量**: `UPPER_SNAKE_CASE`
```typescript
const API_BASE_URL = 'https://api.example.com'
const MAX_RETRY_COUNT = 3
```

**布尔值**: 使用 `is/has/can/should` 前缀
```typescript
const isVisible = true
const hasPermission = false
const canEdit = true
```

## 🧩 组件命名

**组件名**: `PascalCase`
```vue
<UserProfile />
<ProductList />
```

**Props**: 定义用 `camelCase`，使用用 `kebab-case`
```vue
<script setup lang="ts">
interface Props {
  userName: string  // 定义
}
</script>

<template>
  <UserCard :user-name="userName" />  <!-- 使用 -->
</template>
```

**事件**: `kebab-case`
```vue
<script setup lang="ts">
const emit = defineEmits<{
  'user-click': [user: User]
  'data-update': [data: any]
}>()
</script>
```

## 🔍 函数命名

**普通函数**: 动词开头
```typescript
function getUserInfo() {}
function validateEmail() {}
function formatDate() {}
```

**事件处理**: `handle` + 动作 + 对象
```typescript
const handleButtonClick = () => {}
const handleUserSelect = () => {}
```

## 📊 类型命名

**接口**: `PascalCase`
```typescript
interface UserInfo {
  id: number
  name: string
}
```

**类型**: `PascalCase`
```typescript
type UserId = string | number
type HttpMethod = 'GET' | 'POST'
```

## 🎨 CSS 类名

**格式**: `kebab-case`
```css
.user-profile {}
.btn-primary {}
.form-input {}
```
