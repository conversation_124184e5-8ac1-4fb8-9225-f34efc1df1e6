# 🔧 技术栈使用规范

## 🎯 技术栈

- Vue 3 + TypeScript + Vite
- PrimeVue + PrimeIcons
- Pinia + Vue Router 4 + Axios

## 🚀 Vue 3 规范

**使用 `<script setup>`**
```vue
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

const count = ref(0)
const doubleCount = computed(() => count.value * 2)

onMounted(() => {
  console.log('组件已挂载')
})
</script>
```

**类型定义**
```typescript
// ✅ 明确类型
const user = ref<User | null>(null)
const users = ref<User[]>([])

// ❌ 缺少类型
const user = ref(null)
```

## 🎨 PrimeVue 规范

**组件使用**
```vue
<template>
  <Button
    label="确定"
    icon="pi pi-check"
    @click="handleConfirm"
  />

  <DataTable :value="users" :loading="loading">
    <Column field="name" header="姓名" />
  </DataTable>
</template>
```

**CSS 变量**
```vue
<style scoped>
.custom-card {
  background: var(--surface-ground);
  color: var(--text-color);
  border: 1px solid var(--surface-border);
}
</style>
```

## 🌐 API 请求规范

**使用封装的 http**
```typescript
import { http } from '@/api'

// 基础请求
const getUsers = () => http.get<User[]>('/users')
const createUser = (data: CreateUserParams) => http.post<User>('/users', data)
const updateUser = (id: number, data: UpdateUserParams) => http.put<User>(`/users/${id}`, data)
```

**API 模块组织**
```typescript
// api/user.ts
export const userApi = {
  login: (params: LoginParams) => http.post<LoginResponse>('/auth/login', params),
  getCurrentUser: () => http.get<UserInfo>('/user/profile'),
  getUsers: (params: GetUsersParams) => http.get<PageResponse<User>>('/users', params)
}
```

## 🗄️ Pinia 状态管理

**Store 定义**
```typescript
// store/modules/user.ts
export const useUserStore = defineStore('user', () => {
  // State
  const userInfo = ref<UserInfo | null>(null)
  const isLoggedIn = ref(false)

  // Getters
  const userName = computed(() => userInfo.value?.name || '')

  // Actions
  const login = async (params: LoginParams) => {
    const response = await userApi.login(params)
    userInfo.value = response.userInfo
    isLoggedIn.value = true
  }

  return { userInfo, isLoggedIn, userName, login }
})
```

**Store 使用**
```vue
<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/store/modules/user'

const userStore = useUserStore()
const { userInfo, isLoggedIn } = storeToRefs(userStore)
const { login } = userStore
</script>
```

## 🛣️ Vue Router 规范

**路由定义**
```typescript
export const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/dashboard/Index.vue'),
    meta: { title: '首页', requiresAuth: true }
  }
]
```

**路由使用**
```vue
<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goToUser = (id: number) => {
  router.push({ name: 'UserProfile', params: { id } })
}
</script>
```

## 📦 导入规范

**导入顺序**
```typescript
// 1. Vue 相关
import { ref, computed } from 'vue'

// 2. 第三方库
import axios from 'axios'

// 3. 项目内部
import { http } from '@/api'
import UserCard from '@/components/business/UserCard.vue'
```

**使用路径别名**
```typescript
// ✅ 使用别名
import { http } from '@/api'

// ❌ 相对路径
import { http } from '../../../api'
```
