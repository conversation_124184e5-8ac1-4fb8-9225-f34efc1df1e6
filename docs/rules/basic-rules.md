# 🎯 基础开发规则

## 📦 包管理规范

**强制使用 pnpm**
```bash
# ✅ 正确
pnpm install
pnpm add vue
pnpm add -D typescript

# ❌ 禁止
npm install
yarn add vue
```

**依赖管理**
- 生产依赖: `pnpm add`
- 开发依赖: `pnpm add -D`
- 及时移除未使用的依赖

## 🎨 图标使用规范

**PrimeIcons v7.0.0 - 禁止编造图标**

```vue
<!-- ✅ 正确使用 -->
<i class="pi pi-user"></i>
<Button icon="pi pi-save" label="保存" />

<!-- ❌ 禁止编造 -->
<i class="pi pi-fake-icon"></i>
<i class="pi pi-custom-icon"></i>
```

**常用图标**
```typescript
// 基础操作
'pi pi-plus'     // 添加
'pi pi-pencil'   // 编辑
'pi pi-trash'    // 删除
'pi pi-check'    // 确认
'pi pi-times'    // 取消

// 导航
'pi pi-home'     // 首页
'pi pi-search'   // 搜索
'pi pi-refresh'  // 刷新

// 状态
'pi pi-spin pi-spinner'  // 加载中
```

## 🔧 Git 提交规范

**提交格式**
```bash
feat: 添加用户管理功能
fix: 修复登录样式问题
docs: 更新文档
```

**分支命名**
```bash
feature/user-management
bugfix/login-issue
hotfix/security-patch
```

## 📋 提交前检查

```bash
pnpm lint       # 代码检查
pnpm build      # 构建测试
```

**检查清单**
- [ ] 使用正确图标类名
- [ ] 添加必要类型定义
- [ ] 移除 console.log
- [ ] 测试主要功能

## 🚨 违规处理

- **严重**: 使用不存在图标 → 拒绝合并
- **中等**: 使用 npm 而非 pnpm → 要求修改
- **轻微**: 缺少类型定义 → 代码审查提醒
