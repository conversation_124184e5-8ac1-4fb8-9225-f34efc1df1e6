# 📋 组件开发规范

## 🎯 设计原则

1. **单一职责** - 每个组件只负责一个功能
2. **可复用性** - 设计时考虑复用场景
3. **可配置性** - 通过 Props 提供配置
4. **可扩展性** - 预留插槽和事件

## 🏗️ 组件结构

```vue
<template>
  <div :class="componentClass">
    <slot name="header" />
    <div class="content">
      <!-- 主要内容 -->
    </div>
    <slot name="footer" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Props {
  title: string
  visible?: boolean
  size?: 'small' | 'medium' | 'large'
}

interface Emits {
  'update:visible': [visible: boolean]
  'confirm': [data: any]
}

const props = withDefaults(defineProps<Props>(), {
  visible: true,
  size: 'medium'
})

const emit = defineEmits<Emits>()

const loading = ref(false)

const componentClass = computed(() => [
  'component-wrapper',
  `component-wrapper--${props.size}`
])

const handleConfirm = () => {
  emit('confirm', { success: true })
}
</script>

<style scoped>
.component-wrapper {
  padding: 1rem;
}
</style>
```

## 📝 Props 规范

**类型定义**
```typescript
interface Props {
  title: string                    // 必需
  visible?: boolean               // 可选
  size?: 'small' | 'medium'       // 联合类型
  config?: { showHeader: boolean } // 对象类型
  formatter?: (value: any) => string // 函数类型
}

const props = withDefaults(defineProps<Props>(), {
  visible: true,
  size: 'medium'
})
```

**Props 验证**
```typescript
const isValidEmail = computed(() => {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(props.email)
})
```

## 🎭 事件规范

**事件定义**
```typescript
interface Emits {
  'click': [event: MouseEvent]
  'item-select': [item: Item, index: number]
  'update:modelValue': [value: string]
}

const emit = defineEmits<Emits>()

const handleClick = (item: Item, index: number) => {
  emit('item-select', item, index)
}
```

**v-model 支持**
```vue
<script setup lang="ts">
interface Props {
  modelValue: string
}

const inputValue = computed({
  get: () => props.modelValue,
  set: (value: string) => emit('update:modelValue', value)
})
</script>

<template>
  <input v-model="inputValue" />
</template>
```

## 🎪 插槽规范

**插槽定义**
```vue
<template>
  <div class="card">
    <header v-if="$slots.header">
      <slot name="header" />
    </header>

    <main>
      <slot>默认内容</slot>
    </main>

    <footer>
      <slot name="footer" :data="footerData" />
    </footer>
  </div>
</template>
```

**插槽使用**
```vue
<template>
  <CustomCard>
    <template #header>
      <h2>标题</h2>
    </template>

    <p>内容</p>

    <template #footer="{ data }">
      <span>{{ data.total }}</span>
    </template>
  </CustomCard>
</template>
```

## 🎨 样式规范

**CSS 作用域**
```vue
<style scoped>
.component {
  padding: 1rem;
  background: var(--surface-ground);
  color: var(--text-color);
}

@media (max-width: 768px) {
  .component {
    padding: 0.5rem;
  }
}
</style>
```

**动态样式**
```vue
<script setup lang="ts">
const componentClass = computed(() => [
  'component',
  `component--${props.type}`,
  `component--${props.size}`
])
</script>

<template>
  <div :class="componentClass">
    内容
  </div>
</template>
```

## 🔧 组件通信

**父子通信**
```vue
<!-- 父组件 -->
<template>
  <ChildComponent
    :user-data="userData"
    @user-select="handleUserSelect"
  />
</template>

<script setup lang="ts">
const handleUserSelect = (user: User) => {
  console.log('选中用户:', user)
}
</script>
```

**跨组件通信**
```typescript
// 使用 Pinia
import { useGlobalStore } from '@/store/modules/global'
const globalStore = useGlobalStore()
```

## ✅ 开发检查清单

- [ ] 组件名使用 PascalCase
- [ ] Props 有类型定义
- [ ] 事件名使用 kebab-case
- [ ] 使用 scoped 样式
- [ ] 考虑响应式设计
