# 📋 项目开发规范

## 📚 规范文档目录

本项目的开发规范分为以下几个部分，每个部分都有详细的说明和示例：

### 🎯 [基础开发规则](./docs/rules/basic-rules.md) ⭐
- 包管理规范（必须使用 pnpm）
- 图标使用规范（禁止编造图标）
- 开发工具配置
- Git 使用规范

### 🏗️ [目录结构规范](./docs/rules/directory-structure.md)
- 项目目录组织方式
- 文件分类和命名规则
- 模块化设计原则

### 📝 [命名规范](./docs/rules/naming-conventions.md)
- 文件命名规则
- 变量命名规则
- 组件命名规则

### 🔧 [技术栈使用规范](./docs/rules/tech-stack.md)
- Vue3 组合式 API 使用
- PrimeVue 组件使用
- API 请求规范
- 状态管理规范

### 📦 [导入导出规范](./docs/rules/import-export.md)
- 路径别名使用
- 导入顺序规范
- 模块导出规范

### 🎨 [样式规范](./docs/rules/styling.md)
- CSS 变量使用
- 响应式设计
- 样式组织方式

### 🔍 [TypeScript 规范](./docs/rules/typescript.md)
- 类型定义规范
- 接口设计原则
- 泛型使用规范

### 📋 [组件开发规范](./docs/rules/component-development.md)
- 组件结构规范
- Props 定义规范
- 事件处理规范

### 🚨 [错误处理规范](./docs/rules/error-handling.md)
- API 错误处理
- 组件错误边界
- 用户友好的错误提示

### 📚 [注释规范](./docs/rules/comments.md)
- 函数注释规范
- 组件注释规范
- 代码注释最佳实践

### ✅ [代码质量规范](./docs/rules/code-quality.md)
- 代码检查工具
- 性能优化建议
- 最佳实践

---

## 🎯 核心原则

1. **简单明了** - 代码要易读易懂
2. **一致性** - 遵循统一的命名和结构规范
3. **可维护** - 模块化设计，职责分离
4. **类型安全** - 充分利用 TypeScript
5. **性能优先** - 考虑加载速度和用户体验

## 🚀 快速开始

新团队成员请按以下顺序阅读规范：

1. 📖 [基础开发规则](./docs/rules/basic-rules.md) - **必读**，了解基础开发要求
2. 📖 [目录结构规范](./docs/rules/directory-structure.md) - 了解项目组织方式
3. 📖 [命名规范](./docs/rules/naming-conventions.md) - 掌握命名约定
4. 📖 [技术栈使用规范](./docs/rules/tech-stack.md) - 学习技术栈使用方式
5. 📖 [组件开发规范](./docs/rules/component-development.md) - 开始编写组件

其他规范可以在开发过程中逐步学习和应用。
